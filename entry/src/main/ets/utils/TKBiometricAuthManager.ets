/**
 * 生物识别认证管理器 (门面类)
 * 统一管理指纹、人脸等生物识别功能
 *
 */
import { TKLog } from '@thinkive/tk-harmony-base';
import { userAuth } from '@kit.UserAuthenticationKit';
import { BusinessError } from '@kit.BasicServicesKit';
import { TKBiometricAuthType, TKBiometricAuthResult, TKBiometricAuthCallback } from './biometric/TKBiometricTypes';
import { TKFingerprintAuth } from './biometric/TKFingerprintAuth';
import { TKFaceAuth } from './biometric/TKFaceAuth';
import { TKCompositeAuth } from './biometric/TKCompositeAuth';

// 重新导出类型，保持向后兼容
export { TKBiometricAuthType, TKBiometricAuthResult, TKBiometricAuthCallback };

/**
 * 认证队列项接口
 */
interface AuthenticationQueueItem {
  authType: TKBiometricAuthType;
  callback: TKBiometricAuthCallback;
  resolve: (value: void) => void;
  reject: (reason: Error) => void;
}



/**
 * 生物识别认证管理器
 * 提供统一的生物识别认证入口
 */
export class TKBiometricAuthManager {
  private static instance: TKBiometricAuthManager;

  // 各类型认证器实例
  private fingerprintAuth: TKFingerprintAuth;
  private faceAuth: TKFaceAuth;
  private compositeAuth: TKCompositeAuth;

  // 支持状态缓存
  private supportStatusCache: Map<TKBiometricAuthType, boolean> = new Map();
  private cacheExpireTime: number = 0;
  private readonly CACHE_DURATION = 30000; // 30秒缓存

  // 认证状态管理
  private isAuthenticating: boolean = false;
  private currentAuthType?: TKBiometricAuthType;
  private authenticationQueue: Array<AuthenticationQueueItem> = [];

  // 资源清理标记
  private isDestroyed: boolean = false;

  private constructor() {
    this.fingerprintAuth = new TKFingerprintAuth();
    this.faceAuth = new TKFaceAuth();
    this.compositeAuth = new TKCompositeAuth();
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): TKBiometricAuthManager {
    if (!TKBiometricAuthManager.instance) {
      TKBiometricAuthManager.instance = new TKBiometricAuthManager();
    }
    return TKBiometricAuthManager.instance;
  }

  /**
   * 检查设备是否支持指定的生物识别类型
   * @param authType 认证类型
   * @param useCache 是否使用缓存，默认true
   * @returns Promise<boolean> 是否支持
   */
  public async isAuthTypeSupported(authType: TKBiometricAuthType, useCache: boolean = true): Promise<boolean> {
    try {

      // 检查缓存
      if (useCache && this.isCacheValid()) {
        const cachedResult = this.supportStatusCache.get(authType);
        if (cachedResult !== undefined) {
          TKLog.debug(`[生物识别管理器]:使用缓存结果 ${authType}: ${cachedResult}`);
          return cachedResult;
        }
      }

      let isSupported = false;
      switch (authType) {
        case TKBiometricAuthType.FINGERPRINT:
          isSupported = await this.fingerprintAuth.isSupported();
          break;
        case TKBiometricAuthType.FACE:
          isSupported = await this.faceAuth.isSupported();
          break;
        case TKBiometricAuthType.COMPOSITE:
          isSupported = await this.compositeAuth.isSupported();
          break;
        default:
          TKLog.warn(`[生物识别管理器]:不支持的认证类型: ${authType}`);
          return false;
      }

      // 更新缓存
      this.updateCache(authType, isSupported);
      return isSupported;

    } catch (error) {
      TKLog.error(`[生物识别管理器]:检查认证类型支持状态失败: ${JSON.stringify(error)}`);
      return false;
    }
  }

  /**
   * 检查缓存是否有效
   */
  private isCacheValid(): boolean {
    return Date.now() < this.cacheExpireTime;
  }

  /**
   * 更新缓存
   */
  private updateCache(authType: TKBiometricAuthType, isSupported: boolean): void {
    this.supportStatusCache.set(authType, isSupported);
    this.cacheExpireTime = Date.now() + this.CACHE_DURATION;
  }

  /**
   * 清除缓存
   */
  public clearCache(): void {
    this.supportStatusCache.clear();
    this.cacheExpireTime = 0;
    TKLog.debug(`[生物识别管理器]:缓存已清除`);
  }

  /**
   * 检查是否正在认证
   */
  public isCurrentlyAuthenticating(): boolean {
    return this.isAuthenticating;
  }

  /**
   * 获取当前认证类型
   */
  public getCurrentAuthType(): TKBiometricAuthType | undefined {
    return this.currentAuthType;
  }

  /**
   * 清理所有资源
   */
  public destroy(): void {

    if (this.isDestroyed) {
      return;
    }

    TKLog.info(`[生物识别管理器]:开始清理所有资源`);

    // 取消当前认证
    this.cancelCurrentAuthentication();

    // 清理认证队列
    this.clearAuthenticationQueue();

    // 清理各认证器资源
    this.fingerprintAuth.cancel();
    this.faceAuth.cancel();
    this.compositeAuth.cancel();

    // 清理缓存
    this.clearCache();

    // 重置状态
    this.isAuthenticating = false;
    this.currentAuthType = undefined;
    this.isDestroyed = true;

    TKLog.info(`[生物识别管理器]:资源清理完成`);
  }

  /**
   * 取消当前认证
   */
  private cancelCurrentAuthentication(): void {
    if (this.isAuthenticating && this.currentAuthType) {
      TKLog.info(`[生物识别管理器]:取消当前${this.currentAuthType}认证`);

      switch (this.currentAuthType) {
        case TKBiometricAuthType.FINGERPRINT:
          this.fingerprintAuth.cancel();
          break;
        case TKBiometricAuthType.FACE:
          this.faceAuth.cancel();
          break;
        case TKBiometricAuthType.COMPOSITE:
          this.compositeAuth.cancel();
          break;
      }
    }
  }

  /**
   * 清理认证队列
   */
  private clearAuthenticationQueue(): void {
    if (this.authenticationQueue.length > 0) {
      TKLog.info(`[生物识别管理器]:清理认证队列，待处理请求数: ${this.authenticationQueue.length}`);

      // 拒绝所有待处理的认证请求
      this.authenticationQueue.forEach(item => {
        const errorResult: TKBiometricAuthResult = {
          success: false,
          authType: item.authType,
          errorCode: -4,
          errorMessage: '认证管理器已销毁'
        };
        item.callback.onFailed?.(errorResult);
        item.reject(new Error('认证管理器已销毁'));
      });

      this.authenticationQueue = [];
    }
  }

  /**
   * 获取设备支持的所有生物识别类型
   * @returns Promise<TKBiometricAuthType[]> 支持的认证类型列表
   */
  public async getSupportedAuthTypes(): Promise<TKBiometricAuthType[]> {
    const supportedTypes: TKBiometricAuthType[] = [];

    try {

      // 检查指纹支持
      const fingerprintSupported = await this.isAuthTypeSupported(TKBiometricAuthType.FINGERPRINT);
      if (fingerprintSupported) {
        supportedTypes.push(TKBiometricAuthType.FINGERPRINT);
      }

      // 检查人脸支持
      const faceSupported = await this.isAuthTypeSupported(TKBiometricAuthType.FACE);
      if (faceSupported) {
        supportedTypes.push(TKBiometricAuthType.FACE);
      }

      // 检查复合认证支持 - 需要实际检查复合认证能力，而不是简单的长度判断
      const compositeSupported = await this.isAuthTypeSupported(TKBiometricAuthType.COMPOSITE);
      if (compositeSupported) {
        supportedTypes.push(TKBiometricAuthType.COMPOSITE);
      }

      TKLog.info(`[生物识别管理器]:设备支持的认证类型: ${JSON.stringify(supportedTypes)}`);
      TKLog.debug(`[生物识别管理器]:详细支持状态 - 指纹:${fingerprintSupported}, 人脸:${faceSupported}, 复合:${compositeSupported}`);

      return supportedTypes;
    } catch (error) {
      TKLog.error(`[生物识别管理器]:获取支持的认证类型失败: ${JSON.stringify(error)}`);
      return [];
    }
  }

  /**
   * 执行生物识别认证（支持并发安全和队列管理）
   * @param authType 认证类型
   * @param callback 认证回调
   */
  public async authenticate(authType: TKBiometricAuthType, callback: TKBiometricAuthCallback): Promise<void> {

    if (this.isDestroyed) {
      const errorResult: TKBiometricAuthResult = {
        success: false,
        authType: authType,
        errorCode: -4,
        errorMessage: '认证管理器已销毁'
      };
      callback.onFailed?.(errorResult);
      return;
    }

    return new Promise<void>((resolve, reject) => {
      // 添加到认证队列
      this.authenticationQueue.push({
        authType,
        callback,
        resolve,
        reject
      });

      // 处理队列
      this.processAuthenticationQueue();
    });
  }

  /**
   * 处理认证队列
   */
  private async processAuthenticationQueue(): Promise<void> {
    // 如果正在认证或队列为空，直接返回
    if (this.isAuthenticating || this.authenticationQueue.length === 0) {
      return;
    }

    // 取出队列中的第一个请求
    const authRequest = this.authenticationQueue.shift();
    if (!authRequest) {
      return;
    }

    this.isAuthenticating = true;
    this.currentAuthType = authRequest.authType;

    TKLog.info(`[生物识别管理器]:开始处理${authRequest.authType}认证，队列剩余: ${this.authenticationQueue.length}`);

    try {
      // 检查设备支持状态
      const isSupported = await this.isAuthTypeSupported(authRequest.authType);
      if (!isSupported) {
        const errorResult: TKBiometricAuthResult = {
          success: false,
          authType: authRequest.authType,
          errorCode: -1,
          errorMessage: `设备不支持${authRequest.authType}认证`
        };
        authRequest.callback.onFailed?.(errorResult);
        authRequest.reject(new Error(errorResult.errorMessage));
        this.onAuthenticationComplete();
        return;
      }

      // 创建包装的回调，用于处理认证完成后的清理
      const wrappedCallback: TKBiometricAuthCallback = {
        onSuccess: (result: TKBiometricAuthResult) => {
          authRequest.callback.onSuccess?.(result);
          authRequest.resolve();
          this.onAuthenticationComplete();
        },
        onFailed: (result: TKBiometricAuthResult) => {
          authRequest.callback.onFailed?.(result);
          authRequest.reject(new Error(result.errorMessage));
          this.onAuthenticationComplete();
        },
        onCancel: () => {
          authRequest.callback.onCancel?.();
          authRequest.resolve();
          this.onAuthenticationComplete();
        }
      };

      // 根据认证类型选择对应的认证器
      await this.executeAuthentication(authRequest.authType, wrappedCallback);

    } catch (error) {
      TKLog.error(`[生物识别管理器]:认证执行异常: ${JSON.stringify(error)}`);
      const errorResult: TKBiometricAuthResult = {
        success: false,
        authType: authRequest.authType,
        errorCode: -3,
        errorMessage: `认证执行异常: ${error}`
      };
      authRequest.callback.onFailed?.(errorResult);
      authRequest.reject(error);
      this.onAuthenticationComplete();
    }
  }

  /**
   * 执行具体的认证逻辑
   */
  private async executeAuthentication(authType: TKBiometricAuthType, callback: TKBiometricAuthCallback): Promise<void> {
    switch (authType) {
      case TKBiometricAuthType.FINGERPRINT:
        await this.fingerprintAuth.authenticate(callback);
        break;
      case TKBiometricAuthType.FACE:
        await this.faceAuth.authenticate(callback);
        break;
      case TKBiometricAuthType.COMPOSITE:
        await this.compositeAuth.authenticate(callback);
        break;
      default:
        const errorResult: TKBiometricAuthResult = {
          success: false,
          authType: authType,
          errorCode: -2,
          errorMessage: `不支持的认证类型: ${authType}`
        };
        callback.onFailed?.(errorResult);
        break;
    }
  }

  /**
   * 认证完成后的清理工作
   */
  private onAuthenticationComplete(): void {
    this.isAuthenticating = false;
    this.currentAuthType = undefined;

    TKLog.debug(`[生物识别管理器]:认证完成，继续处理队列`);

    // 继续处理队列中的下一个请求
    setTimeout(() => {
      this.processAuthenticationQueue();
    }, 100); // 短暂延迟，避免过于频繁的调用
  }

  /**
   * 取消当前认证
   */
  public cancelAuthentication(): void {
    TKLog.info(`[生物识别管理器]:取消当前认证`);

    // 取消当前认证
    this.cancelCurrentAuthentication();

    // 清理认证队列
    this.clearAuthenticationQueue();

    // 重置状态
    this.isAuthenticating = false;
    this.currentAuthType = undefined;
  }
}

/**
 * 导出默认实例
 */
export default TKBiometricAuthManager.getInstance();
